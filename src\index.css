@tailwind base;
@tailwind components;
@tailwind utilities;

/* AI Portfolio Design System - Inspired by n8n.io
Light-themed neon gradients with soft-glow animations
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Base colors - light theme with subtle neon accents */
    --background: 250 100% 99%;
    --foreground: 240 10% 15%;

    /* Card system with glass morphism */
    --card: 250 100% 98%;
    --card-foreground: 240 10% 15%;

    --popover: 250 100% 98%;
    --popover-foreground: 240 10% 15%;

    /* Neon gradient primary colors */
    --primary: 260 100% 60%;
    --primary-foreground: 250 100% 99%;
    --primary-glow: 280 100% 70%;

    /* Secondary neon accents */
    --secondary: 280 100% 96%;
    --secondary-foreground: 260 100% 60%;

    /* Muted backgrounds with subtle glow */
    --muted: 250 50% 97%;
    --muted-foreground: 240 5% 50%;

    /* Accent neon colors */
    --accent: 190 100% 90%;
    --accent-foreground: 260 100% 60%;
    --accent-glow: 190 100% 60%;

    /* Status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 250 100% 99%;

    /* Borders and inputs with glow */
    --border: 250 20% 90%;
    --input: 250 20% 90%;
    --ring: 260 100% 60%;

    /* Neon gradient variables */
    --gradient-primary: linear-gradient(135deg, hsl(260 100% 60%), hsl(280 100% 70%), hsl(300 100% 75%));
    --gradient-secondary: linear-gradient(135deg, hsl(190 100% 60%), hsl(220 100% 70%), hsl(260 100% 75%));
    --gradient-accent: linear-gradient(45deg, hsl(260 100% 60%), hsl(190 100% 60%));
    
    /* Glow effects */
    --glow-primary: 0 0 30px hsl(260 100% 60% / 0.3);
    --glow-secondary: 0 0 30px hsl(190 100% 60% / 0.3);
    --glow-strong: 0 0 50px hsl(260 100% 60% / 0.5);
    
    /* Glass morphism */
    --glass-bg: hsl(250 100% 98% / 0.8);
    --glass-border: hsl(250 100% 95% / 0.5);

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark theme neon colors */
    --background: 222 84% 4%;
    --foreground: 250 100% 98%;

    /* Dark glass morphism */
    --card: 222 84% 6%;
    --card-foreground: 250 100% 98%;

    --popover: 222 84% 6%;
    --popover-foreground: 250 100% 98%;

    /* Enhanced neon primary for dark mode */
    --primary: 260 100% 70%;
    --primary-foreground: 222 84% 4%;
    --primary-glow: 280 100% 80%;

    /* Dark secondary with neon accent */
    --secondary: 240 100% 12%;
    --secondary-foreground: 250 100% 98%;

    /* Dark muted with subtle glow */
    --muted: 240 50% 8%;
    --muted-foreground: 240 20% 70%;

    /* Enhanced accent for dark mode */
    --accent: 190 100% 15%;
    --accent-foreground: 190 100% 80%;
    --accent-glow: 190 100% 70%;

    /* Dark theme status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 250 100% 98%;

    /* Dark borders and inputs with neon glow */
    --border: 240 50% 15%;
    --input: 240 50% 15%;
    --ring: 260 100% 70%;

    /* Enhanced dark theme gradients */
    --gradient-primary: linear-gradient(135deg, hsl(260 100% 70%), hsl(280 100% 80%), hsl(300 100% 85%));
    --gradient-secondary: linear-gradient(135deg, hsl(190 100% 70%), hsl(220 100% 80%), hsl(260 100% 85%));
    --gradient-accent: linear-gradient(45deg, hsl(260 100% 70%), hsl(190 100% 70%));
    
    /* Enhanced glow effects for dark mode */
    --glow-primary: 0 0 40px hsl(260 100% 70% / 0.5);
    --glow-secondary: 0 0 40px hsl(190 100% 70% / 0.5);
    --glow-strong: 0 0 60px hsl(260 100% 70% / 0.7);
    
    /* Dark glass morphism */
    --glass-bg: hsl(222 84% 6% / 0.8);
    --glass-border: hsl(240 50% 15% / 0.8);

    --radius: 1rem;
  }
}

@layer base {
  * {
    @apply border-border;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    transition: background-color 0.5s ease, color 0.5s ease;
  }

  /* Smooth theme transitions for all components */
  .glass-card,
  .floating-icon-wrapper,
  .contact-card,
  .project-card {
    transition: all 0.3s ease;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }
}

@layer components {
  /* Mobile-first responsive neon button */
  .btn-neon {
    @apply relative overflow-hidden bg-gradient-to-r from-primary to-primary-glow text-primary-foreground;
    @apply rounded-full px-6 py-3 text-sm font-semibold transition-all duration-300 sm:px-8 sm:py-3 sm:text-base;
    @apply hover:shadow-[var(--glow-primary)] hover:scale-105 active:scale-95;
  }
  
  .btn-neon::before {
    @apply absolute inset-0 bg-gradient-to-r from-primary-glow to-accent-glow opacity-0 transition-opacity duration-300;
    content: '';
  }
  
  .btn-neon:hover::before {
    @apply opacity-100;
  }

  /* Mobile-responsive glass morphism cards */
  .glass-card {
    @apply backdrop-blur-sm border rounded-xl p-4 sm:rounded-2xl sm:p-6;
    background: var(--glass-bg);
    border-color: var(--glass-border);
    box-shadow: var(--glow-secondary);
  }
  
  .glass-card:hover {
    @apply transform -translate-y-1 sm:-translate-y-2 transition-all duration-300;
    box-shadow: var(--glow-strong);
  }

  /* Mobile-responsive neon text effects */
  .neon-text {
    @apply bg-gradient-to-r from-primary via-primary-glow to-accent-glow bg-clip-text text-transparent;
  }
  
  .neon-text-glow {
    @apply neon-text;
    text-shadow: var(--glow-primary);
  }

  /* Mobile-responsive animated gradient backgrounds */
  .gradient-bg {
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
  }
  
  .gradient-bg-secondary {
    background: var(--gradient-secondary);
    background-size: 400% 400%;
    animation: gradientShift 6s ease infinite;
  }

  /* Mobile-optimized floating animation */
  .float {
    animation: float 4s ease-in-out infinite;
  }
  
  .float-delayed {
    animation: float 4s ease-in-out infinite 2s;
  }

  /* Mobile-responsive skill progress bars */
  .skill-bar {
    @apply h-1.5 sm:h-2 bg-muted rounded-full overflow-hidden;
  }
  
  .skill-progress {
    @apply h-full bg-gradient-to-r from-primary to-accent-glow rounded-full transition-all duration-1000;
    box-shadow: var(--glow-primary);
  }

  /* Mobile navigation styles */
  .mobile-nav-item {
    @apply w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300;
    @apply hover:bg-muted hover:shadow-glow-secondary active:scale-95;
  }
  
  .mobile-nav-item.active {
    @apply bg-gradient-primary text-primary-foreground shadow-glow;
  }

  /* Mobile-optimized project cards */
  .project-card {
    @apply glass-card cursor-pointer h-full flex flex-col;
    @apply hover:shadow-[var(--glow-strong)] active:scale-95 transition-all duration-300;
    @apply hover:scale-[1.02] sm:hover:scale-105;
  }

  /* Line clamp utilities for better text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced contact section styling */
  .contact-card {
    @apply border-2 border-transparent;
    background: var(--glass-bg);
    border-image: linear-gradient(135deg, hsl(var(--primary) / 0.3), hsl(var(--accent-glow) / 0.3)) 1;
  }

  .contact-card:hover {
    @apply border-primary/50;
    box-shadow: var(--glow-strong);
  }

  .contact-info-item:hover {
    @apply transform translate-x-1 transition-all duration-300;
  }

  .social-link:hover {
    @apply transform -translate-y-1 transition-all duration-300;
  }

  /* Floating Icons System */
  .floating-icon {
    animation: floatIcon 6s ease-in-out infinite;
  }

  .floating-icon-wrapper {
    @apply relative p-2 rounded-full backdrop-blur-sm;
    @apply border border-primary/20 bg-background/80 dark:bg-background/60;
    @apply hover:border-primary/50 hover:bg-background/90 dark:hover:bg-background/80;
    @apply transition-all duration-300 hover:shadow-glow;
  }

  .floating-icon-svg {
    @apply text-primary/70 dark:text-primary/80;
    @apply hover:text-primary dark:hover:text-primary-glow;
    filter: drop-shadow(0 0 4px hsl(var(--primary) / 0.3));
  }

  .floating-icon-glow {
    @apply absolute inset-0 rounded-full opacity-0 transition-opacity duration-300;
    @apply group-hover:opacity-100;
    background: radial-gradient(circle, hsl(var(--primary) / 0.2) 0%, transparent 70%);
  }

  /* Responsive floating icons */
  @media (max-width: 640px) {
    .floating-icon-wrapper {
      @apply p-1.5;
    }

    .floating-icon-svg {
      @apply w-4 h-4;
    }
  }

  /* Enhanced Theme Toggle */
  .theme-toggle {
    @apply hover:scale-110 active:scale-95;
  }

  .theme-toggle:hover {
    box-shadow: var(--glow-strong);
  }

  /* Mobile contact form styling */
  .mobile-form-input {
    @apply glass-card focus:shadow-glow transition-all duration-300 text-base;
    @apply focus:ring-2 focus:ring-primary/50 focus:border-primary;
    @apply hover:border-primary/30;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  .loading-shimmer {
    @apply relative overflow-hidden;
  }

  .loading-shimmer::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent;
    content: '';
    animation: shimmer 2s infinite;
  }

  /* Loading Spinner */
  .loading-spinner {
    @apply relative flex items-center justify-center;
  }

  .loading-spinner-inner {
    @apply relative w-full h-full;
  }

  .loading-dot {
    @apply absolute w-2 h-2 bg-primary rounded-full;
    animation: loadingDots 1.4s infinite ease-in-out both;
  }

  .loading-dot-1 {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: -0.32s;
  }

  .loading-dot-2 {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: -0.16s;
  }

  .loading-dot-3 {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
  }
}

@layer utilities {
  /* Animation keyframes */
  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: var(--glow-primary); }
    50% { box-shadow: var(--glow-strong); }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: var(--glow-primary);
      transform: scale(1);
    }
    50% {
      box-shadow: var(--glow-strong);
      transform: scale(1.02);
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.4);
      opacity: 0;
    }
  }

  @keyframes gridMove {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(60px, 60px);
    }
  }

  @keyframes particleFloat {
    0%, 100% {
      transform: translateY(0px) translateX(0px);
    }
    25% {
      transform: translateY(-10px) translateX(5px);
    }
    50% {
      transform: translateY(-5px) translateX(-5px);
    }
    75% {
      transform: translateY(-15px) translateX(10px);
    }
  }

  @keyframes neuralPulse {
    0%, 100% {
      opacity: 0.1;
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.05);
    }
  }

  @keyframes floatIcon {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-8px) rotate(2deg);
    }
    50% {
      transform: translateY(-4px) rotate(-1deg);
    }
    75% {
      transform: translateY(-12px) rotate(1deg);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes fadeInUpMobile {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeftMobile {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRightMobile {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes loadingDots {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mobile-optimized animation classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-ripple {
    animation: ripple 0.6s ease-out;
  }

  /* Enhanced responsive utility classes */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  .mobile-section {
    @apply py-12 sm:py-16 lg:py-20 xl:py-24;
  }

  .mobile-heading {
    @apply text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold;
  }

  .mobile-subheading {
    @apply text-lg sm:text-xl lg:text-2xl xl:text-3xl;
  }

  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 xl:gap-10;
  }

  /* Enhanced mobile animations */
  @media (max-width: 640px) {
    .animate-fade-in-up {
      animation: fadeInUpMobile 0.8s ease-out;
    }

    .animate-slide-in-left {
      animation: slideInLeftMobile 0.8s ease-out;
    }

    .animate-slide-in-right {
      animation: slideInRightMobile 0.8s ease-out;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .floating-icon {
      animation: none !important;
    }

    .animate-pulse {
      animation: none !important;
    }
  }
}
